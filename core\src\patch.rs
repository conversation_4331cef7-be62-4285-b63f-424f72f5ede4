use arien_common::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use crate::{<PERSON>C<PERSON>e, FileChangeType, PatchResult};
use std::path::Path;

/// Patch application system for file modifications
pub struct PatchApplicator {
    allowed_roots: Vec<std::path::PathBuf>,
    dry_run: bool,
}

impl PatchApplicator {
    /// Create a new patch applicator
    pub fn new(allowed_roots: Vec<std::path::PathBuf>) -> Self {
        Self {
            allowed_roots,
            dry_run: false,
        }
    }

    /// Enable dry run mode (don't actually apply changes)
    pub fn with_dry_run(mut self) -> Self {
        self.dry_run = true;
        self
    }

    /// Apply a set of file changes
    pub async fn apply_changes(&self, changes: Vec<FileChange>) -> Result<Vec<PatchResult>> {
        let mut results = Vec::new();

        for change in changes {
            let result = self.apply_single_change(change).await;
            results.push(result);
        }

        Ok(results)
    }

    async fn apply_single_change(&self, change: <PERSON><PERSON><PERSON><PERSON>) -> PatchResult {
        // Validate the path is within allowed roots
        if let Err(e) = self.validate_path(&change.path) {
            return PatchResult {
                path: change.path,
                success: false,
                error: Some(e.to_string()),
            };
        }

        let result = match change.change_type {
            FileChangeType::Create => self.create_file(&change.path, &change.content).await,
            FileChangeType::Update => self.update_file(&change.path, &change.content, &change.old_content).await,
            FileChangeType::Delete => self.delete_file(&change.path).await,
            FileChangeType::Move { new_path } => self.move_file(&change.path, &new_path).await,
        };

        match result {
            Ok(()) => PatchResult {
                path: change.path,
                success: true,
                error: None,
            },
            Err(e) => PatchResult {
                path: change.path,
                success: false,
                error: Some(e.to_string()),
            },
        }
    }

    fn validate_path(&self, path: &SafePath) -> Result<()> {
        let path_buf = path.as_path();
        
        // Check if path is within allowed roots
        for root in &self.allowed_roots {
            if path_buf.starts_with(root) {
                return Ok(());
            }
        }

        Err(ArienError::permission_denied(format!(
            "Path {} is not within allowed roots",
            path_buf.display()
        )))
    }

    async fn create_file(&self, path: &SafePath, content: &Option<String>) -> Result<()> {
        let path_buf = path.as_path();
        
        if path_buf.exists() {
            return Err(ArienError::invalid_input(format!(
                "File already exists: {}",
                path_buf.display()
            )));
        }

        if self.dry_run {
            tracing::info!("DRY RUN: Would create file {}", path_buf.display());
            return Ok(());
        }

        // Create parent directories if they don't exist
        if let Some(parent) = path_buf.parent() {
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| ArienError::Io(e))?;
        }

        let content = content.as_deref().unwrap_or("");
        tokio::fs::write(path_buf, content).await
            .map_err(|e| ArienError::Io(e))?;

        tracing::info!("Created file {}", path_buf.display());
        Ok(())
    }

    async fn update_file(&self, path: &SafePath, new_content: &Option<String>, old_content: &Option<String>) -> Result<()> {
        let path_buf = path.as_path();
        
        if !path_buf.exists() {
            return Err(ArienError::invalid_input(format!(
                "File does not exist: {}",
                path_buf.display()
            )));
        }

        // Verify old content if provided
        if let Some(expected_old) = old_content {
            let current_content = tokio::fs::read_to_string(path_buf).await
                .map_err(|e| ArienError::Io(e))?;
            
            if current_content != *expected_old {
                return Err(ArienError::invalid_input(
                    "File content has changed since patch was created"
                ));
            }
        }

        if self.dry_run {
            tracing::info!("DRY RUN: Would update file {}", path_buf.display());
            return Ok(());
        }

        // Create backup
        let backup_path = format!("{}.backup", path_buf.display());
        tokio::fs::copy(path_buf, &backup_path).await
            .map_err(|e| ArienError::Io(e))?;

        // Write new content
        let content = new_content.as_deref().unwrap_or("");
        tokio::fs::write(path_buf, content).await
            .map_err(|e| ArienError::Io(e))?;

        tracing::info!("Updated file {} (backup: {})", path_buf.display(), backup_path);
        Ok(())
    }

    async fn delete_file(&self, path: &SafePath) -> Result<()> {
        let path_buf = path.as_path();
        
        if !path_buf.exists() {
            return Err(ArienError::invalid_input(format!(
                "File does not exist: {}",
                path_buf.display()
            )));
        }

        if self.dry_run {
            tracing::info!("DRY RUN: Would delete file {}", path_buf.display());
            return Ok(());
        }

        // Create backup before deletion
        let backup_path = format!("{}.deleted", path_buf.display());
        tokio::fs::copy(path_buf, &backup_path).await
            .map_err(|e| ArienError::Io(e))?;

        tokio::fs::remove_file(path_buf).await
            .map_err(|e| ArienError::Io(e))?;

        tracing::info!("Deleted file {} (backup: {})", path_buf.display(), backup_path);
        Ok(())
    }

    async fn move_file(&self, old_path: &SafePath, new_path: &SafePath) -> Result<()> {
        let old_path_buf = old_path.as_path();
        let new_path_buf = new_path.as_path();
        
        if !old_path_buf.exists() {
            return Err(ArienError::invalid_input(format!(
                "Source file does not exist: {}",
                old_path_buf.display()
            )));
        }

        if new_path_buf.exists() {
            return Err(ArienError::invalid_input(format!(
                "Destination file already exists: {}",
                new_path_buf.display()
            )));
        }

        // Validate new path is also within allowed roots
        self.validate_path(new_path)?;

        if self.dry_run {
            tracing::info!("DRY RUN: Would move file {} to {}", old_path_buf.display(), new_path_buf.display());
            return Ok(());
        }

        // Create parent directories for new path if they don't exist
        if let Some(parent) = new_path_buf.parent() {
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| ArienError::Io(e))?;
        }

        tokio::fs::rename(old_path_buf, new_path_buf).await
            .map_err(|e| ArienError::Io(e))?;

        tracing::info!("Moved file {} to {}", old_path_buf.display(), new_path_buf.display());
        Ok(())
    }
}

/// Parse a patch from text format
pub struct PatchParser;

impl PatchParser {
    /// Parse a unified diff format patch
    pub fn parse_unified_diff(patch_text: &str) -> Result<Vec<FileChange>> {
        // This is a simplified parser - a full implementation would handle
        // proper unified diff format with line numbers, context, etc.
        
        let mut changes = Vec::new();
        let lines: Vec<&str> = patch_text.lines().collect();
        let mut i = 0;

        while i < lines.len() {
            let line = lines[i];
            
            if line.starts_with("--- ") {
                // Start of a file diff
                if i + 1 < lines.len() && lines[i + 1].starts_with("+++ ") {
                    let old_file = line[4..].trim();
                    let new_file = lines[i + 1][4..].trim();
                    
                    i += 2;
                    
                    // Determine change type
                    if old_file == "/dev/null" {
                        // New file
                        let content = Self::extract_added_content(&lines[i..]);
                        changes.push(FileChange {
                            path: SafePath::new(new_file)?,
                            change_type: FileChangeType::Create,
                            content: Some(content),
                            old_content: None,
                        });
                    } else if new_file == "/dev/null" {
                        // Deleted file
                        changes.push(FileChange {
                            path: SafePath::new(old_file)?,
                            change_type: FileChangeType::Delete,
                            content: None,
                            old_content: None,
                        });
                    } else {
                        // Modified file
                        let content = Self::extract_added_content(&lines[i..]);
                        changes.push(FileChange {
                            path: SafePath::new(new_file)?,
                            change_type: FileChangeType::Update,
                            content: Some(content),
                            old_content: None,
                        });
                    }
                }
            }
            
            i += 1;
        }

        Ok(changes)
    }

    fn extract_added_content(lines: &[&str]) -> String {
        let mut content = String::new();
        
        for line in lines {
            if line.starts_with('+') && !line.starts_with("+++") {
                content.push_str(&line[1..]);
                content.push('\n');
            } else if line.starts_with("@@") {
                break;
            }
        }
        
        content
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_patch_applicator_create_file() {
        let temp_dir = TempDir::new().unwrap();
        let applicator = PatchApplicator::new(vec![temp_dir.path().to_path_buf()]);
        
        let file_path = temp_dir.path().join("test.txt");
        let safe_path = SafePath::new(&file_path).unwrap();
        
        let change = FileChange {
            path: safe_path,
            change_type: FileChangeType::Create,
            content: Some("Hello, world!".to_string()),
            old_content: None,
        };
        
        let results = applicator.apply_changes(vec![change]).await.unwrap();
        assert_eq!(results.len(), 1);
        assert!(results[0].success);
        
        // Verify file was created
        assert!(file_path.exists());
        let content = tokio::fs::read_to_string(&file_path).await.unwrap();
        assert_eq!(content, "Hello, world!");
    }

    #[tokio::test]
    async fn test_patch_applicator_dry_run() {
        let temp_dir = TempDir::new().unwrap();
        let applicator = PatchApplicator::new(vec![temp_dir.path().to_path_buf()])
            .with_dry_run();
        
        let file_path = temp_dir.path().join("test.txt");
        let safe_path = SafePath::new(&file_path).unwrap();
        
        let change = FileChange {
            path: safe_path,
            change_type: FileChangeType::Create,
            content: Some("Hello, world!".to_string()),
            old_content: None,
        };
        
        let results = applicator.apply_changes(vec![change]).await.unwrap();
        assert_eq!(results.len(), 1);
        assert!(results[0].success);
        
        // Verify file was NOT created (dry run)
        assert!(!file_path.exists());
    }
}
