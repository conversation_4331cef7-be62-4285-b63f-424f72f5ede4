use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Prompt definition
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Prompt {
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub arguments: Option<Vec<PromptArgument>>,
}

/// Prompt argument
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PromptArgument {
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub required: Option<bool>,
}

/// Prompts list request
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ListPromptsRequest {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cursor: Option<String>,
}

/// Prompts list response
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ListPromptsResponse {
    pub prompts: Vec<Prompt>,
    #[serde(rename = "nextCursor", skip_serializing_if = "Option::is_none")]
    pub next_cursor: Option<String>,
}

/// Get prompt request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetPromptRequest {
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub arguments: Option<HashMap<String, String>>,
}

/// Get prompt response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetPromptResponse {
    pub description: Option<String>,
    pub messages: Vec<PromptMessage>,
}

/// Prompt message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptMessage {
    pub role: PromptRole,
    pub content: PromptContent,
}

/// Prompt role
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum PromptRole {
    User,
    Assistant,
    System,
}

/// Prompt content
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum PromptContent {
    #[serde(rename = "text")]
    Text { text: String },
    #[serde(rename = "image")]
    Image {
        data: String,
        #[serde(rename = "mimeType")]
        mime_type: String,
    },
    #[serde(rename = "resource")]
    Resource {
        resource: crate::ResourceReference,
    },
}

/// Prompt list changed notification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptListChangedNotification {}

impl Prompt {
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            description: None,
            arguments: None,
        }
    }

    pub fn with_description(mut self, description: impl Into<String>) -> Self {
        self.description = Some(description.into());
        self
    }

    pub fn with_arguments(mut self, arguments: Vec<PromptArgument>) -> Self {
        self.arguments = Some(arguments);
        self
    }

    pub fn add_argument(mut self, argument: PromptArgument) -> Self {
        let mut args = self.arguments.unwrap_or_default();
        args.push(argument);
        self.arguments = Some(args);
        self
    }
}

impl PromptArgument {
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            description: None,
            required: None,
        }
    }

    pub fn with_description(mut self, description: impl Into<String>) -> Self {
        self.description = Some(description.into());
        self
    }

    pub fn required(mut self) -> Self {
        self.required = Some(true);
        self
    }

    pub fn optional(mut self) -> Self {
        self.required = Some(false);
        self
    }
}

impl PromptMessage {
    pub fn user(content: PromptContent) -> Self {
        Self {
            role: PromptRole::User,
            content,
        }
    }

    pub fn assistant(content: PromptContent) -> Self {
        Self {
            role: PromptRole::Assistant,
            content,
        }
    }

    pub fn system(content: PromptContent) -> Self {
        Self {
            role: PromptRole::System,
            content,
        }
    }

    pub fn user_text(text: impl Into<String>) -> Self {
        Self::user(PromptContent::Text {
            text: text.into(),
        })
    }

    pub fn assistant_text(text: impl Into<String>) -> Self {
        Self::assistant(PromptContent::Text {
            text: text.into(),
        })
    }

    pub fn system_text(text: impl Into<String>) -> Self {
        Self::system(PromptContent::Text {
            text: text.into(),
        })
    }
}

impl PromptContent {
    pub fn text(text: impl Into<String>) -> Self {
        Self::Text {
            text: text.into(),
        }
    }

    pub fn image(data: impl Into<String>, mime_type: impl Into<String>) -> Self {
        Self::Image {
            data: data.into(),
            mime_type: mime_type.into(),
        }
    }

    pub fn resource(uri: impl Into<String>, text: Option<String>) -> Self {
        Self::Resource {
            resource: crate::ResourceReference {
                uri: uri.into(),
                text,
            },
        }
    }
}

impl GetPromptResponse {
    pub fn new(messages: Vec<PromptMessage>) -> Self {
        Self {
            description: None,
            messages,
        }
    }

    pub fn with_description(mut self, description: impl Into<String>) -> Self {
        self.description = Some(description.into());
        self
    }
}
