[workspace]
resolver = "2"
members = [
    "core",
    "cli", 
    "tui",
    "exec",
    "mcp-client",
    "mcp-server", 
    "mcp-types",
    "apply-patch",
    "linux-sandbox",
    "execpolicy",
    "login",
    "ansi-escape",
    "common"
]

[workspace.dependencies]
# Core async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-util = "0.7"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# Error handling
thiserror = "1.0"
anyhow = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# CLI
clap = { version = "4.0", features = ["derive"] }

# TUI
ratatui = "0.28"
crossterm = "0.28"

# HTTP client
reqwest = { version = "0.12", features = ["json", "stream"] }

# Filesystem
walkdir = "2.0"

# Process management
nix = "0.29"

# Cryptography
sha2 = "0.10"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# UUID generation
uuid = { version = "1.0", features = ["v4", "serde"] }

# Channels
flume = "0.11"

# Regex
regex = "1.0"

# Base64
base64 = "0.22"

# Image handling
image = "0.25"

[profile.release]
lto = "fat"
strip = "symbols"
codegen-units = 1
panic = "abort"

[workspace.lints.clippy]
expect_used = "deny"
unwrap_used = "deny"
panic = "deny"
