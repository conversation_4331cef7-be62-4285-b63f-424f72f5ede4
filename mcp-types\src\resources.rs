use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Resource definition
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Resource {
    pub uri: String,
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(rename = "mimeType", skip_serializing_if = "Option::is_none")]
    pub mime_type: Option<String>,
}

/// Resources list request
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ListResourcesRequest {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cursor: Option<String>,
}

/// Resources list response
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ListResourcesResponse {
    pub resources: Vec<Resource>,
    #[serde(rename = "nextCursor", skip_serializing_if = "Option::is_none")]
    pub next_cursor: Option<String>,
}

/// Resource read request
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ReadResourceRequest {
    pub uri: String,
}

/// Resource read response
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ReadResourceResponse {
    pub contents: Vec<ResourceContent>,
}

/// Resource content
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ResourceContent {
    #[serde(rename = "text")]
    Text {
        text: String,
        #[serde(rename = "mimeType", skip_serializing_if = "Option::is_none")]
        mime_type: Option<String>,
    },
    #[serde(rename = "blob")]
    Blob {
        blob: String, // Base64 encoded
        #[serde(rename = "mimeType")]
        mime_type: String,
    },
}

/// Resource subscribe request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscribeResourceRequest {
    pub uri: String,
}

/// Resource unsubscribe request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnsubscribeResourceRequest {
    pub uri: String,
}

/// Resource updated notification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUpdatedNotification {
    pub uri: String,
}

/// Resource list changed notification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceListChangedNotification {}

impl Resource {
    pub fn new(uri: impl Into<String>, name: impl Into<String>) -> Self {
        Self {
            uri: uri.into(),
            name: name.into(),
            description: None,
            mime_type: None,
        }
    }

    pub fn with_description(mut self, description: impl Into<String>) -> Self {
        self.description = Some(description.into());
        self
    }

    pub fn with_mime_type(mut self, mime_type: impl Into<String>) -> Self {
        self.mime_type = Some(mime_type.into());
        self
    }
}

impl ResourceContent {
    pub fn text(text: impl Into<String>) -> Self {
        Self::Text {
            text: text.into(),
            mime_type: None,
        }
    }

    pub fn text_with_mime_type(text: impl Into<String>, mime_type: impl Into<String>) -> Self {
        Self::Text {
            text: text.into(),
            mime_type: Some(mime_type.into()),
        }
    }

    pub fn blob(blob: impl Into<String>, mime_type: impl Into<String>) -> Self {
        Self::Blob {
            blob: blob.into(),
            mime_type: mime_type.into(),
        }
    }
}

impl ReadResourceResponse {
    pub fn new(contents: Vec<ResourceContent>) -> Self {
        Self { contents }
    }

    pub fn text(text: impl Into<String>) -> Self {
        Self::new(vec![ResourceContent::text(text)])
    }

    pub fn text_with_mime_type(text: impl Into<String>, mime_type: impl Into<String>) -> Self {
        Self::new(vec![ResourceContent::text_with_mime_type(text, mime_type)])
    }

    pub fn blob(blob: impl Into<String>, mime_type: impl Into<String>) -> Self {
        Self::new(vec![ResourceContent::blob(blob, mime_type)])
    }
}
