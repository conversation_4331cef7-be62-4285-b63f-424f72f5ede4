use clap::{Parser, Subcommand};
use arien_core::{Config, ConfigOverride, Arien, Op, SessionConfig, InputItem};
use arien_common::{SessionId, Result, ArienError};
use std::io::{self, Write};

#[derive(Parser)]
#[command(name = "arien")]
#[command(about = "Arien AI - AI-powered coding assistant")]
#[command(version = "0.1.0")]
struct Cli {
    #[command(subcommand)]
    command: Option<Commands>,

    /// Configuration overrides in key=value format
    #[arg(short = 'c', long = "config", value_parser = ConfigOverride::parse)]
    config_overrides: Vec<ConfigOverride>,

    /// Configuration profile to use
    #[arg(short = 'p', long = "profile")]
    profile: Option<String>,

    /// Log level
    #[arg(long = "log-level", default_value = "info")]
    log_level: String,
}

#[derive(Subcommand)]
enum Commands {
    /// Interactive mode (launches TUI)
    Interactive,
    
    /// Execute a single prompt and exit
    Exec {
        /// The prompt to execute
        prompt: String,
        
        /// Working directory
        #[arg(short = 'd', long = "dir")]
        working_dir: Option<String>,
        
        /// Auto-approve all operations
        #[arg(long = "auto-approve")]
        auto_approve: bool,
    },
    
    /// Run as MCP server
    McpServer,
    
    /// Protocol mode (JSON over stdin/stdout)
    Protocol,
    
    /// Debug commands
    Debug {
        #[command(subcommand)]
        debug_command: DebugCommands,
    },
}

#[derive(Subcommand)]
enum DebugCommands {
    /// Test sandbox functionality
    TestSandbox {
        /// Command to test
        command: Vec<String>,
    },
    
    /// Show configuration
    ShowConfig,
    
    /// List available MCP tools
    ListTools,
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::new(&cli.log_level))
        .init();

    // Load configuration
    let config = Config::load_with_overrides(cli.config_overrides, cli.profile).await?;

    match cli.command {
        Some(Commands::Interactive) => {
            interactive_mode(config).await
        }
        Some(Commands::Exec { prompt, working_dir, auto_approve }) => {
            exec_mode(config, prompt, working_dir, auto_approve).await
        }
        Some(Commands::McpServer) => {
            mcp_server_mode(config).await
        }
        Some(Commands::Protocol) => {
            protocol_mode(config).await
        }
        Some(Commands::Debug { debug_command }) => {
            debug_mode(config, debug_command).await
        }
        None => {
            // Default to interactive mode
            interactive_mode(config).await
        }
    }
}

async fn interactive_mode(config: Config) -> Result<()> {
    println!("🤖 Arien AI - Interactive Mode");
    println!("Type 'help' for commands, 'quit' to exit");
    
    let arien = Arien::new(config).await?;
    let session_id = SessionId::new();
    
    // Configure session
    let session_config = SessionConfig {
        model: None,
        model_provider: None,
        instructions: Some("You are a helpful AI coding assistant.".to_string()),
        approval_policy: None,
        sandbox_policy: None,
        shell_environment_policy: None,
        writable_roots: None,
        mcp_servers: None,
    };
    
    arien.submit(session_id, Op::ConfigureSession { config: session_config }).await?;
    
    // Wait for session configured event
    let event = arien.recv_event().await?;
    println!("Session configured: {:?}", event);

    loop {
        print!("arien> ");
        io::stdout().flush().unwrap();
        
        let mut input = String::new();
        io::stdin().read_line(&mut input).unwrap();
        let input = input.trim();
        
        if input.is_empty() {
            continue;
        }
        
        match input {
            "quit" | "exit" => break,
            "help" => {
                println!("Commands:");
                println!("  help    - Show this help");
                println!("  quit    - Exit the program");
                println!("  <text>  - Send message to AI");
            }
            _ => {
                // Send user input
                let items = vec![InputItem::Text {
                    content: input.to_string(),
                }];
                
                arien.submit(session_id, Op::UserInput { items }).await?;
                
                // Process events
                loop {
                    let event = arien.recv_event().await?;
                    match event.event_type {
                        arien_core::EventType::ModelResponse { content, partial } => {
                            if partial {
                                print!("{}", content);
                                io::stdout().flush().unwrap();
                            } else {
                                println!("{}", content);
                            }
                        }
                        arien_core::EventType::ConversationEnded => {
                            break;
                        }
                        arien_core::EventType::Error { error, .. } => {
                            eprintln!("Error: {}", error);
                            break;
                        }
                        _ => {
                            println!("Event: {:?}", event.event_type);
                        }
                    }
                }
            }
        }
    }
    
    println!("Goodbye! 👋");
    Ok(())
}

async fn exec_mode(config: Config, prompt: String, _working_dir: Option<String>, _auto_approve: bool) -> Result<()> {
    println!("🤖 Arien AI - Exec Mode");
    
    let arien = Arien::new(config).await?;
    let session_id = SessionId::new();
    
    // Configure session for non-interactive mode
    let session_config = SessionConfig {
        model: None,
        model_provider: None,
        instructions: Some("You are a helpful AI coding assistant. Be concise and direct.".to_string()),
        approval_policy: Some(arien_core::AskForApproval::Never),
        sandbox_policy: None,
        shell_environment_policy: None,
        writable_roots: None,
        mcp_servers: None,
    };
    
    arien.submit(session_id, Op::ConfigureSession { config: session_config }).await?;
    
    // Wait for session configured
    arien.recv_event().await?;
    
    // Send prompt
    let items = vec![InputItem::Text { content: prompt }];
    arien.submit(session_id, Op::UserInput { items }).await?;
    
    // Process events until conversation ends
    loop {
        let event = arien.recv_event().await?;
        match event.event_type {
            arien_core::EventType::ModelResponse { content, partial } => {
                if partial {
                    print!("{}", content);
                    io::stdout().flush().unwrap();
                } else {
                    println!("{}", content);
                }
            }
            arien_core::EventType::ConversationEnded => {
                break;
            }
            arien_core::EventType::Error { error, .. } => {
                eprintln!("Error: {}", error);
                return Err(ArienError::unknown(error));
            }
            _ => {
                // Log other events in exec mode
                tracing::debug!("Event: {:?}", event.event_type);
            }
        }
    }
    
    Ok(())
}

async fn mcp_server_mode(_config: Config) -> Result<()> {
    println!("🤖 Arien AI - MCP Server Mode");
    println!("MCP server functionality not yet implemented");
    Ok(())
}

async fn protocol_mode(_config: Config) -> Result<()> {
    println!("🤖 Arien AI - Protocol Mode");
    println!("Protocol mode functionality not yet implemented");
    Ok(())
}

async fn debug_mode(config: Config, debug_command: DebugCommands) -> Result<()> {
    match debug_command {
        DebugCommands::TestSandbox { command } => {
            println!("🔍 Testing sandbox with command: {:?}", command);
            println!("Sandbox testing not yet implemented");
        }
        DebugCommands::ShowConfig => {
            println!("🔧 Current configuration:");
            println!("{:#?}", config);
        }
        DebugCommands::ListTools => {
            println!("🛠️  Available MCP tools:");
            println!("MCP tool listing not yet implemented");
        }
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cli_parsing() {
        // Test basic command parsing
        let cli = Cli::try_parse_from(&["arien", "exec", "hello world"]).unwrap();
        
        match cli.command {
            Some(Commands::Exec { prompt, .. }) => {
                assert_eq!(prompt, "hello world");
            }
            _ => panic!("Expected exec command"),
        }
    }

    #[test]
    fn test_config_override_parsing() {
        let cli = Cli::try_parse_from(&[
            "arien", 
            "-c", "model=gpt-4",
            "-c", "approval_policy=never",
            "interactive"
        ]).unwrap();
        
        assert_eq!(cli.config_overrides.len(), 2);
        assert_eq!(cli.config_overrides[0].key, "model");
        assert_eq!(cli.config_overrides[0].value, "gpt-4");
    }
}
