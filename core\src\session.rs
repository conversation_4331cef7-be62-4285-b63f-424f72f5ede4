use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use tokio::sync::Notify;
use flume::Sender;
use arien_common::{Result, ArienError, SessionId, SafePath};
use crate::{
    Config, Event, EventType, Submission, Op, ModelClient, McpConnectionManager,
    AskForApproval, SandboxPolicy, ShellEnvironmentPolicy, SessionConfig,
    InputItem, FileChange, PatchResult,
};

/// Session manages the state and configuration for a single AI interaction session
pub struct Session {
    session_id: SessionId,
    client: ModelClient,
    tx_event: Sender<Event>,
    ctrl_c: Arc<Notify>,
    cwd: PathBuf,
    instructions: Option<String>,
    approval_policy: AskForApproval,
    sandbox_policy: SandboxPolicy,
    shell_environment_policy: ShellEnvironmentPolicy,
    writable_roots: Mutex<Vec<PathBuf>>,
    mcp_connection_manager: McpConnectionManager,
    conversation_history: Mutex<Vec<ConversationMessage>>,
    pending_approvals: Mutex<HashMap<String, PendingApproval>>,
}

/// Conversation message
#[derive(Debug, Clone)]
pub struct ConversationMessage {
    pub role: MessageRole,
    pub content: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Message role
#[derive(Debug, Clone)]
pub enum MessageRole {
    User,
    Assistant,
    System,
}

/// Pending approval
#[derive(Debug, Clone)]
pub enum PendingApproval {
    Execution {
        command: Vec<String>,
        working_dir: Option<SafePath>,
    },
    Patch {
        patch_id: String,
        changes: Vec<FileChange>,
    },
    ToolCall {
        tool_call_id: String,
        tool_name: String,
        arguments: HashMap<String, serde_json::Value>,
    },
}

impl Session {
    /// Create a new session
    pub async fn new(
        session_id: SessionId,
        tx_event: Sender<Event>,
        ctrl_c: Arc<Notify>,
        config: Config,
    ) -> Result<Self> {
        let client = ModelClient::new(config.clone()).await?;
        let mcp_connection_manager = McpConnectionManager::new(config.mcp_servers.clone()).await?;
        
        let cwd = std::env::current_dir()
            .map_err(|e| ArienError::config(format!("Failed to get current directory: {}", e)))?;

        Ok(Self {
            session_id,
            client,
            tx_event,
            ctrl_c,
            cwd,
            instructions: config.instructions,
            approval_policy: config.approval_policy,
            sandbox_policy: config.sandbox_policy,
            shell_environment_policy: config.shell_environment_policy,
            writable_roots: Mutex::new(config.writable_roots),
            mcp_connection_manager,
            conversation_history: Mutex::new(Vec::new()),
            pending_approvals: Mutex::new(HashMap::new()),
        })
    }

    /// Handle a submission
    pub async fn handle_submission(&mut self, submission: Submission) -> Result<()> {
        match submission.op {
            Op::ConfigureSession { config } => {
                self.configure_session(submission.id, config).await
            }
            Op::UserInput { items } => {
                self.handle_user_input(submission.id, items).await
            }
            Op::ExecApproval { approved, command } => {
                self.handle_exec_approval(submission.id, approved, command).await
            }
            Op::PatchApproval { approved, patch_id } => {
                self.handle_patch_approval(submission.id, approved, patch_id).await
            }
            Op::ToolApproval { approved, tool_call_id } => {
                self.handle_tool_approval(submission.id, approved, tool_call_id).await
            }
            Op::Interrupt => {
                self.handle_interrupt(submission.id).await
            }
            Op::Reset => {
                self.handle_reset(submission.id).await
            }
        }
    }

    async fn configure_session(&mut self, submission_id: arien_common::SubmissionId, config: SessionConfig) -> Result<()> {
        // Apply session configuration
        if let Some(instructions) = config.instructions {
            self.instructions = Some(instructions);
        }
        
        if let Some(approval_policy) = config.approval_policy {
            self.approval_policy = approval_policy;
        }
        
        if let Some(sandbox_policy) = config.sandbox_policy {
            self.sandbox_policy = sandbox_policy;
        }
        
        if let Some(shell_environment_policy) = config.shell_environment_policy {
            self.shell_environment_policy = shell_environment_policy;
        }
        
        if let Some(writable_roots) = config.writable_roots {
            let mut roots = self.writable_roots.lock()
                .map_err(|_| ArienError::unknown("Failed to lock writable roots"))?;
            *roots = writable_roots.into_iter().map(|p| p.into_path_buf()).collect();
        }

        // Update model if specified
        if let Some(model) = config.model {
            self.client.set_model(model).await?;
        }

        if let Some(model_provider) = config.model_provider {
            self.client.set_provider(model_provider).await?;
        }

        // Update MCP servers if specified
        if let Some(mcp_servers) = config.mcp_servers {
            self.mcp_connection_manager.update_servers(mcp_servers).await?;
        }

        // Send session configured event
        self.send_event(submission_id, EventType::SessionConfigured {
            session_id: self.session_id,
        }).await
    }

    async fn handle_user_input(&mut self, submission_id: arien_common::SubmissionId, items: Vec<InputItem>) -> Result<()> {
        // Add user input to conversation history
        for item in &items {
            match item {
                InputItem::Text { content } => {
                    self.add_to_conversation(MessageRole::User, content.clone());
                }
                InputItem::Image { .. } => {
                    self.add_to_conversation(MessageRole::User, "[Image]".to_string());
                }
                InputItem::LocalImage { path } => {
                    self.add_to_conversation(MessageRole::User, format!("[Image: {}]", path));
                }
                InputItem::File { path, .. } => {
                    self.add_to_conversation(MessageRole::User, format!("[File: {}]", path));
                }
            }
        }

        // Send conversation started event
        self.send_event(submission_id, EventType::ConversationStarted).await?;

        // Process the input with the model
        self.process_with_model(submission_id, items).await
    }

    async fn handle_exec_approval(&mut self, submission_id: arien_common::SubmissionId, approved: bool, command: Vec<String>) -> Result<()> {
        let approval_key = format!("exec:{}", command.join(" "));
        
        let mut pending = self.pending_approvals.lock()
            .map_err(|_| ArienError::unknown("Failed to lock pending approvals"))?;
        
        if let Some(PendingApproval::Execution { command: pending_command, working_dir }) = pending.remove(&approval_key) {
            drop(pending);
            
            if approved && command == pending_command {
                self.execute_command(submission_id, command, working_dir).await
            } else {
                self.send_event(submission_id, EventType::Info {
                    message: "Command execution cancelled by user".to_string(),
                }).await
            }
        } else {
            self.send_event(submission_id, EventType::Warning {
                message: "No pending execution found for approval".to_string(),
            }).await
        }
    }

    async fn handle_patch_approval(&mut self, submission_id: arien_common::SubmissionId, approved: bool, patch_id: String) -> Result<()> {
        let mut pending = self.pending_approvals.lock()
            .map_err(|_| ArienError::unknown("Failed to lock pending approvals"))?;
        
        if let Some(PendingApproval::Patch { patch_id: pending_id, changes }) = pending.remove(&patch_id) {
            drop(pending);
            
            if approved && patch_id == pending_id {
                self.apply_patch(submission_id, patch_id, changes).await
            } else {
                self.send_event(submission_id, EventType::Info {
                    message: "Patch application cancelled by user".to_string(),
                }).await
            }
        } else {
            self.send_event(submission_id, EventType::Warning {
                message: "No pending patch found for approval".to_string(),
            }).await
        }
    }

    async fn handle_tool_approval(&mut self, submission_id: arien_common::SubmissionId, approved: bool, tool_call_id: arien_common::ToolCallId) -> Result<()> {
        let tool_call_id_str = tool_call_id.to_string();
        
        let mut pending = self.pending_approvals.lock()
            .map_err(|_| ArienError::unknown("Failed to lock pending approvals"))?;
        
        if let Some(PendingApproval::ToolCall { tool_call_id: pending_id, tool_name, arguments }) = pending.remove(&tool_call_id_str) {
            drop(pending);
            
            if approved && tool_call_id_str == pending_id {
                self.execute_tool_call(submission_id, tool_call_id, tool_name, arguments).await
            } else {
                self.send_event(submission_id, EventType::Info {
                    message: "Tool call cancelled by user".to_string(),
                }).await
            }
        } else {
            self.send_event(submission_id, EventType::Warning {
                message: "No pending tool call found for approval".to_string(),
            }).await
        }
    }

    async fn handle_interrupt(&mut self, submission_id: arien_common::SubmissionId) -> Result<()> {
        // Clear pending approvals
        {
            let mut pending = self.pending_approvals.lock()
                .map_err(|_| ArienError::unknown("Failed to lock pending approvals"))?;
            pending.clear();
        }

        // Send interrupt signal
        self.ctrl_c.notify_waiters();

        self.send_event(submission_id, EventType::Info {
            message: "Session interrupted".to_string(),
        }).await
    }

    async fn handle_reset(&mut self, submission_id: arien_common::SubmissionId) -> Result<()> {
        // Clear conversation history
        {
            let mut history = self.conversation_history.lock()
                .map_err(|_| ArienError::unknown("Failed to lock conversation history"))?;
            history.clear();
        }

        // Clear pending approvals
        {
            let mut pending = self.pending_approvals.lock()
                .map_err(|_| ArienError::unknown("Failed to lock pending approvals"))?;
            pending.clear();
        }

        self.send_event(submission_id, EventType::Info {
            message: "Session reset".to_string(),
        }).await
    }

    async fn process_with_model(&mut self, submission_id: arien_common::SubmissionId, items: Vec<InputItem>) -> Result<()> {
        // This is a placeholder - in a real implementation, this would:
        // 1. Convert items to model input format
        // 2. Stream response from the model
        // 3. Process any tool calls or function calls
        // 4. Handle execution requests and patches
        
        // For now, just send a simple response
        self.send_event(submission_id, EventType::ModelResponse {
            content: "I'm a placeholder response. The full model integration will be implemented next.".to_string(),
            partial: false,
        }).await?;

        self.send_event(submission_id, EventType::ConversationEnded).await
    }

    async fn execute_command(&mut self, submission_id: arien_common::SubmissionId, command: Vec<String>, working_dir: Option<SafePath>) -> Result<()> {
        // Placeholder for command execution
        self.send_event(submission_id, EventType::ExecStarted {
            command: command.clone(),
            working_dir: working_dir.unwrap_or_else(|| SafePath::new(&self.cwd).unwrap()),
        }).await?;

        self.send_event(submission_id, EventType::ExecOutput {
            output: "Command execution not yet implemented".to_string(),
            is_stderr: false,
        }).await?;

        self.send_event(submission_id, EventType::ExecCompleted {
            exit_code: 0,
            duration_ms: 100,
        }).await
    }

    async fn apply_patch(&mut self, submission_id: arien_common::SubmissionId, patch_id: String, changes: Vec<FileChange>) -> Result<()> {
        // Placeholder for patch application
        let results: Vec<PatchResult> = changes.into_iter().map(|change| {
            PatchResult {
                path: change.path,
                success: true,
                error: None,
            }
        }).collect();

        self.send_event(submission_id, EventType::PatchApplied {
            patch_id,
            results,
        }).await
    }

    async fn execute_tool_call(&mut self, submission_id: arien_common::SubmissionId, tool_call_id: arien_common::ToolCallId, tool_name: String, arguments: HashMap<String, serde_json::Value>) -> Result<()> {
        // Placeholder for tool call execution
        self.send_event(submission_id, EventType::ToolResult {
            tool_call_id,
            result: crate::ToolResult::success("Tool call not yet implemented"),
        }).await
    }

    async fn send_event(&self, submission_id: arien_common::SubmissionId, event_type: EventType) -> Result<()> {
        let event = Event::new(submission_id, self.session_id, event_type);
        self.tx_event.send_async(event).await
            .map_err(|_| ArienError::InternalAgentDied)
    }

    fn add_to_conversation(&self, role: MessageRole, content: String) {
        if let Ok(mut history) = self.conversation_history.lock() {
            history.push(ConversationMessage {
                role,
                content,
                timestamp: chrono::Utc::now(),
            });
        }
    }
}
