use arien_core::{Config, Arien, Event, Op, SessionConfig, InputItem};
use arien_common::{Result, ArienError, SessionId};
use crate::{ChatWidget, AppEvent};
use crossterm::event::{KeyCode, KeyEvent, KeyModifiers};
use ratatui::{
    backend::Backend,
    layout::{Constraint, Direction, Layout},
    style::{Color, Style},
    text::{Line, Span},
    widgets::{Block, Borders, Paragraph},
    Frame, Terminal,
};

/// Main TUI application state
pub struct App {
    state: AppState,
    arien: Arien,
    session_id: SessionId,
    should_quit: bool,
}

/// Application state
pub enum AppState {
    Chat { widget: Box<ChatWidget> },
    Login { message: String },
    GitWarning { message: String },
}

impl App {
    /// Create a new TUI application
    pub async fn new(config: Config) -> Result<Self> {
        let arien = Arien::new(config).await?;
        let session_id = SessionId::new();
        
        // Configure session
        let session_config = SessionConfig {
            model: None,
            model_provider: None,
            instructions: Some("You are a helpful AI coding assistant.".to_string()),
            approval_policy: None,
            sandbox_policy: None,
            shell_environment_policy: None,
            writable_roots: None,
            mcp_servers: None,
        };
        
        arien.submit(session_id, Op::ConfigureSession { config: session_config }).await?;
        
        let chat_widget = Box::new(ChatWidget::new());
        
        Ok(Self {
            state: AppState::Chat { widget: chat_widget },
            arien,
            session_id,
            should_quit: false,
        })
    }

    /// Run the TUI application
    pub async fn run<B: Backend>(&mut self, terminal: &mut Terminal<B>) -> Result<()> {
        // Start event processing task
        let arien_events = self.start_event_processing().await?;
        
        loop {
            // Draw the UI
            terminal.draw(|f| self.draw(f))?;
            
            // Handle events
            if let Ok(app_event) = arien_events.try_recv() {
                self.handle_app_event(app_event).await?;
            }
            
            // Handle keyboard input
            if crossterm::event::poll(std::time::Duration::from_millis(50))? {
                if let crossterm::event::Event::Key(key) = crossterm::event::read()? {
                    self.handle_key_event(key).await?;
                }
            }
            
            if self.should_quit {
                break;
            }
        }
        
        Ok(())
    }

    async fn start_event_processing(&self) -> Result<flume::Receiver<AppEvent>> {
        let (tx, rx) = flume::unbounded();
        
        // Clone arien for the background task
        // Note: In a real implementation, we'd need to handle this differently
        // since Arien doesn't implement Clone. This is a placeholder.
        
        tokio::spawn(async move {
            // This would process events from arien and convert them to AppEvents
            // For now, it's just a placeholder
            loop {
                tokio::time::sleep(std::time::Duration::from_millis(100)).await;
                // Would receive events from arien and send AppEvents
            }
        });
        
        Ok(rx)
    }

    async fn handle_app_event(&mut self, event: AppEvent) -> Result<()> {
        match &mut self.state {
            AppState::Chat { widget } => {
                widget.handle_app_event(event).await?;
            }
            _ => {}
        }
        Ok(())
    }

    async fn handle_key_event(&mut self, key: KeyEvent) -> Result<()> {
        // Global key bindings
        match (key.modifiers, key.code) {
            (KeyModifiers::CONTROL, KeyCode::Char('c')) => {
                self.should_quit = true;
                return Ok(());
            }
            (KeyModifiers::CONTROL, KeyCode::Char('d')) => {
                self.should_quit = true;
                return Ok(());
            }
            _ => {}
        }

        // State-specific key handling
        match &mut self.state {
            AppState::Chat { widget } => {
                widget.handle_key_event(key, &self.arien, self.session_id).await?;
            }
            AppState::Login { .. } => {
                // Handle login screen keys
                match key.code {
                    KeyCode::Enter => {
                        // Transition to chat
                        let chat_widget = Box::new(ChatWidget::new());
                        self.state = AppState::Chat { widget: chat_widget };
                    }
                    _ => {}
                }
            }
            AppState::GitWarning { .. } => {
                // Handle git warning screen keys
                match key.code {
                    KeyCode::Enter | KeyCode::Char('y') => {
                        // Proceed to chat
                        let chat_widget = Box::new(ChatWidget::new());
                        self.state = AppState::Chat { widget: chat_widget };
                    }
                    KeyCode::Char('n') | KeyCode::Esc => {
                        self.should_quit = true;
                    }
                    _ => {}
                }
            }
        }

        Ok(())
    }

    fn draw<B: Backend>(&self, f: &mut Frame<B>) {
        match &self.state {
            AppState::Chat { widget } => {
                widget.draw(f);
            }
            AppState::Login { message } => {
                self.draw_login_screen(f, message);
            }
            AppState::GitWarning { message } => {
                self.draw_git_warning_screen(f, message);
            }
        }
    }

    fn draw_login_screen<B: Backend>(&self, f: &mut Frame<B>, message: &str) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Percentage(40),
                Constraint::Percentage(20),
                Constraint::Percentage(40),
            ])
            .split(f.size());

        let login_block = Block::default()
            .title("Arien AI - Login")
            .borders(Borders::ALL)
            .style(Style::default().fg(Color::Cyan));

        let login_text = Paragraph::new(vec![
            Line::from(vec![
                Span::styled("🤖 Welcome to Arien AI", Style::default().fg(Color::Green)),
            ]),
            Line::from(""),
            Line::from(message),
            Line::from(""),
            Line::from("Press Enter to continue..."),
        ])
        .block(login_block)
        .style(Style::default().fg(Color::White));

        f.render_widget(login_text, chunks[1]);
    }

    fn draw_git_warning_screen<B: Backend>(&self, f: &mut Frame<B>, message: &str) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Percentage(40),
                Constraint::Percentage(20),
                Constraint::Percentage(40),
            ])
            .split(f.size());

        let warning_block = Block::default()
            .title("Git Repository Warning")
            .borders(Borders::ALL)
            .style(Style::default().fg(Color::Yellow));

        let warning_text = Paragraph::new(vec![
            Line::from(vec![
                Span::styled("⚠️  Git Repository Detected", Style::default().fg(Color::Yellow)),
            ]),
            Line::from(""),
            Line::from(message),
            Line::from(""),
            Line::from("Do you want to continue? (y/n)"),
        ])
        .block(warning_block)
        .style(Style::default().fg(Color::White));

        f.render_widget(warning_text, chunks[1]);
    }

    pub fn should_quit(&self) -> bool {
        self.should_quit
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use arien_core::Config;

    #[tokio::test]
    async fn test_app_creation() {
        let config = Config::default();
        let app = App::new(config).await;
        assert!(app.is_ok());
    }
}
