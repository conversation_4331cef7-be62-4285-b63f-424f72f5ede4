use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Tool definition
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Tool {
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(rename = "inputSchema")]
    pub input_schema: ToolInputSchema,
}

/// Tool input schema (JSON Schema)
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ToolInputSchema {
    #[serde(rename = "type")]
    pub schema_type: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub properties: Option<HashMap<String, serde_json::Value>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub required: Option<Vec<String>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub additionalProperties: Option<bool>,
}

/// Tools list request
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ListToolsRequest {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cursor: Option<String>,
}

/// Tools list response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ListToolsResponse {
    pub tools: Vec<Tool>,
    #[serde(rename = "nextCursor", skip_serializing_if = "Option::is_none")]
    pub next_cursor: Option<String>,
}

/// Tool call request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CallToolRequest {
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub arguments: Option<HashMap<String, serde_json::Value>>,
}

/// Tool call response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CallToolResponse {
    pub content: Vec<ToolResponseContent>,
    #[serde(rename = "isError", skip_serializing_if = "Option::is_none")]
    pub is_error: Option<bool>,
}

/// Tool response content
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ToolResponseContent {
    #[serde(rename = "text")]
    Text { text: String },
    #[serde(rename = "image")]
    Image {
        data: String,
        #[serde(rename = "mimeType")]
        mime_type: String,
    },
    #[serde(rename = "resource")]
    Resource {
        resource: ResourceReference,
    },
}

/// Resource reference
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceReference {
    pub uri: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub text: Option<String>,
}

impl Tool {
    pub fn new(name: impl Into<String>, description: Option<String>) -> Self {
        Self {
            name: name.into(),
            description,
            input_schema: ToolInputSchema::default(),
        }
    }

    pub fn with_schema(mut self, schema: ToolInputSchema) -> Self {
        self.input_schema = schema;
        self
    }
}

impl Default for ToolInputSchema {
    fn default() -> Self {
        Self {
            schema_type: "object".to_string(),
            properties: None,
            required: None,
            additionalProperties: Some(false),
        }
    }
}

impl ToolInputSchema {
    pub fn object() -> Self {
        Self::default()
    }

    pub fn with_properties(mut self, properties: HashMap<String, serde_json::Value>) -> Self {
        self.properties = Some(properties);
        self
    }

    pub fn with_required(mut self, required: Vec<String>) -> Self {
        self.required = Some(required);
        self
    }

    pub fn add_string_property(
        mut self,
        name: impl Into<String>,
        description: Option<String>,
        required: bool,
    ) -> Self {
        let mut properties = self.properties.unwrap_or_default();
        let mut prop = serde_json::json!({
            "type": "string"
        });
        
        if let Some(desc) = description {
            prop["description"] = serde_json::Value::String(desc);
        }
        
        properties.insert(name.into(), prop);
        self.properties = Some(properties);

        if required {
            let mut req = self.required.unwrap_or_default();
            req.push(name.into());
            self.required = Some(req);
        }

        self
    }

    pub fn add_number_property(
        mut self,
        name: impl Into<String>,
        description: Option<String>,
        required: bool,
    ) -> Self {
        let mut properties = self.properties.unwrap_or_default();
        let mut prop = serde_json::json!({
            "type": "number"
        });
        
        if let Some(desc) = description {
            prop["description"] = serde_json::Value::String(desc);
        }
        
        properties.insert(name.into(), prop);
        self.properties = Some(properties);

        if required {
            let mut req = self.required.unwrap_or_default();
            req.push(name.into());
            self.required = Some(req);
        }

        self
    }

    pub fn add_boolean_property(
        mut self,
        name: impl Into<String>,
        description: Option<String>,
        required: bool,
    ) -> Self {
        let mut properties = self.properties.unwrap_or_default();
        let mut prop = serde_json::json!({
            "type": "boolean"
        });
        
        if let Some(desc) = description {
            prop["description"] = serde_json::Value::String(desc);
        }
        
        properties.insert(name.into(), prop);
        self.properties = Some(properties);

        if required {
            let mut req = self.required.unwrap_or_default();
            req.push(name.into());
            self.required = Some(req);
        }

        self
    }
}

impl CallToolResponse {
    pub fn success(content: Vec<ToolResponseContent>) -> Self {
        Self {
            content,
            is_error: Some(false),
        }
    }

    pub fn error(message: impl Into<String>) -> Self {
        Self {
            content: vec![ToolResponseContent::Text {
                text: message.into(),
            }],
            is_error: Some(true),
        }
    }

    pub fn text(text: impl Into<String>) -> Self {
        Self::success(vec![ToolResponseContent::Text {
            text: text.into(),
        }])
    }
}

impl ToolResponseContent {
    pub fn text(text: impl Into<String>) -> Self {
        Self::Text {
            text: text.into(),
        }
    }

    pub fn image(data: impl Into<String>, mime_type: impl Into<String>) -> Self {
        Self::Image {
            data: data.into(),
            mime_type: mime_type.into(),
        }
    }

    pub fn resource(uri: impl Into<String>, text: Option<String>) -> Self {
        Self::Resource {
            resource: ResourceReference {
                uri: uri.into(),
                text,
            },
        }
    }
}
