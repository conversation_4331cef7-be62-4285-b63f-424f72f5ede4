use std::path::Path;
use tokio::fs;
use crate::{Result, ArienError};

/// Utility functions for file operations
pub struct FileUtils;

impl FileUtils {
    /// Read file contents as string
    pub async fn read_to_string(path: impl AsRef<Path>) -> Result<String> {
        fs::read_to_string(path).await.map_err(ArienError::from)
    }

    /// Write string to file
    pub async fn write_string(path: impl AsRef<Path>, content: &str) -> Result<()> {
        fs::write(path, content).await.map_err(ArienError::from)
    }

    /// Check if file exists
    pub async fn exists(path: impl AsRef<Path>) -> bool {
        fs::metadata(path).await.is_ok()
    }

    /// Create directory if it doesn't exist
    pub async fn create_dir_all(path: impl AsRef<Path>) -> Result<()> {
        fs::create_dir_all(path).await.map_err(ArienError::from)
    }

    /// Get file extension
    pub fn get_extension(path: impl AsRef<Path>) -> Option<String> {
        path.as_ref()
            .extension()
            .and_then(|ext| ext.to_str())
            .map(|s| s.to_lowercase())
    }

    /// Validate that path is within allowed root
    pub fn validate_path_within_root(path: impl AsRef<Path>, root: impl AsRef<Path>) -> Result<()> {
        let path = path.as_ref().canonicalize().map_err(ArienError::from)?;
        let root = root.as_ref().canonicalize().map_err(ArienError::from)?;
        
        if !path.starts_with(&root) {
            return Err(ArienError::permission_denied(
                format!("Path {} is not within allowed root {}", path.display(), root.display())
            ));
        }
        
        Ok(())
    }
}

/// Utility functions for string operations
pub struct StringUtils;

impl StringUtils {
    /// Truncate string to max length with ellipsis
    pub fn truncate(s: &str, max_len: usize) -> String {
        if s.len() <= max_len {
            s.to_string()
        } else {
            format!("{}...", &s[..max_len.saturating_sub(3)])
        }
    }

    /// Escape string for shell usage
    pub fn shell_escape(s: &str) -> String {
        if s.chars().all(|c| c.is_alphanumeric() || c == '_' || c == '-' || c == '.') {
            s.to_string()
        } else {
            format!("'{}'", s.replace('\'', "'\"'\"'"))
        }
    }

    /// Extract command name from command line
    pub fn extract_command_name(command: &[String]) -> Option<&str> {
        command.first().and_then(|cmd| {
            Path::new(cmd)
                .file_name()
                .and_then(|name| name.to_str())
        })
    }
}

/// Utility functions for environment operations
pub struct EnvUtils;

impl EnvUtils {
    /// Get environment variable with error handling
    pub fn get_var(key: &str) -> Result<String> {
        std::env::var(key).map_err(|_| ArienError::EnvVar(key.to_string()))
    }

    /// Get environment variable with default
    pub fn get_var_or_default(key: &str, default: &str) -> String {
        std::env::var(key).unwrap_or_else(|_| default.to_string())
    }

    /// Check if running in CI environment
    pub fn is_ci() -> bool {
        std::env::var("CI").is_ok() || 
        std::env::var("GITHUB_ACTIONS").is_ok() ||
        std::env::var("GITLAB_CI").is_ok() ||
        std::env::var("JENKINS_URL").is_ok()
    }

    /// Get home directory
    pub fn home_dir() -> Result<std::path::PathBuf> {
        dirs::home_dir().ok_or_else(|| ArienError::config("Could not determine home directory"))
    }

    /// Get config directory
    pub fn config_dir() -> Result<std::path::PathBuf> {
        dirs::config_dir()
            .map(|dir| dir.join("arien"))
            .ok_or_else(|| ArienError::config("Could not determine config directory"))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_string_truncate() {
        assert_eq!(StringUtils::truncate("hello", 10), "hello");
        assert_eq!(StringUtils::truncate("hello world", 8), "hello...");
        assert_eq!(StringUtils::truncate("hi", 2), "hi");
    }

    #[test]
    fn test_shell_escape() {
        assert_eq!(StringUtils::shell_escape("hello"), "hello");
        assert_eq!(StringUtils::shell_escape("hello world"), "'hello world'");
        assert_eq!(StringUtils::shell_escape("hello'world"), "'hello'\"'\"'world'");
    }

    #[test]
    fn test_extract_command_name() {
        assert_eq!(
            StringUtils::extract_command_name(&["ls".to_string(), "-la".to_string()]),
            Some("ls")
        );
        assert_eq!(
            StringUtils::extract_command_name(&["/usr/bin/ls".to_string()]),
            Some("ls")
        );
        assert_eq!(StringUtils::extract_command_name(&[]), None);
    }
}
