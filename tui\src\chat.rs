use arien_core::{<PERSON>en, Op, InputItem};
use arien_common::{Result, SessionId};
use crate::AppEvent;
use crossterm::event::{KeyCode, KeyEvent, KeyModifiers};
use ratatui::{
    backend::Backend,
    layout::{Constraint, Direction, Layout, Rect},
    style::{Color, Style},
    text::{Line, Span},
    widgets::{Block, Borders, List, ListItem, Paragraph, Wrap},
    Frame,
};

/// Chat widget for the TUI
pub struct ChatWidget {
    messages: Vec<ChatMessage>,
    input_buffer: String,
    scroll_offset: usize,
}

/// Chat message
#[derive(Debug, Clone)]
pub struct ChatMessage {
    pub role: MessageRole,
    pub content: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Message role
#[derive(Debug, Clone, PartialEq)]
pub enum MessageRole {
    User,
    Assistant,
    System,
    Error,
}

impl ChatWidget {
    pub fn new() -> Self {
        Self {
            messages: vec![
                ChatMessage {
                    role: MessageRole::System,
                    content: "Welcome to Arien AI! Type your message below and press Enter to send.".to_string(),
                    timestamp: chrono::Utc::now(),
                }
            ],
            input_buffer: String::new(),
            scroll_offset: 0,
        }
    }

    pub async fn handle_key_event(&mut self, key: KeyEvent, arien: &Arien, session_id: SessionId) -> Result<()> {
        match (key.modifiers, key.code) {
            (KeyModifiers::NONE, KeyCode::Enter) => {
                if !self.input_buffer.trim().is_empty() {
                    let content = self.input_buffer.trim().to_string();
                    self.input_buffer.clear();
                    
                    // Add user message to chat
                    self.add_message(MessageRole::User, content.clone());
                    
                    // Send to arien
                    let items = vec![InputItem::Text { content }];
                    arien.submit(session_id, Op::UserInput { items }).await?;
                }
            }
            (KeyModifiers::NONE, KeyCode::Backspace) => {
                self.input_buffer.pop();
            }
            (KeyModifiers::CONTROL, KeyCode::Char('u')) => {
                self.input_buffer.clear();
            }
            (KeyModifiers::NONE, KeyCode::Up) => {
                if self.scroll_offset > 0 {
                    self.scroll_offset -= 1;
                }
            }
            (KeyModifiers::NONE, KeyCode::Down) => {
                if self.scroll_offset < self.messages.len().saturating_sub(1) {
                    self.scroll_offset += 1;
                }
            }
            (KeyModifiers::NONE, KeyCode::PageUp) => {
                self.scroll_offset = self.scroll_offset.saturating_sub(10);
            }
            (KeyModifiers::NONE, KeyCode::PageDown) => {
                self.scroll_offset = (self.scroll_offset + 10).min(self.messages.len().saturating_sub(1));
            }
            (KeyModifiers::NONE, KeyCode::Char(c)) => {
                self.input_buffer.push(c);
            }
            _ => {}
        }

        Ok(())
    }

    pub async fn handle_app_event(&mut self, event: AppEvent) -> Result<()> {
        match event {
            AppEvent::ModelResponse { content, partial: _ } => {
                // For now, just add as a complete message
                // In a real implementation, we'd handle partial responses
                self.add_message(MessageRole::Assistant, content);
            }
            AppEvent::Error { message } => {
                self.add_message(MessageRole::Error, message);
            }
            AppEvent::Info { message } => {
                self.add_message(MessageRole::System, message);
            }
        }
        Ok(())
    }

    pub fn draw<B: Backend>(&self, f: &mut Frame<B>) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Min(3),
                Constraint::Length(3),
            ])
            .split(f.size());

        self.draw_messages(f, chunks[0]);
        self.draw_input(f, chunks[1]);
    }

    fn draw_messages<B: Backend>(&self, f: &mut Frame<B>, area: Rect) {
        let messages_block = Block::default()
            .title("Chat")
            .borders(Borders::ALL)
            .style(Style::default().fg(Color::Cyan));

        let visible_messages: Vec<ListItem> = self.messages
            .iter()
            .skip(self.scroll_offset)
            .map(|msg| {
                let style = match msg.role {
                    MessageRole::User => Style::default().fg(Color::Green),
                    MessageRole::Assistant => Style::default().fg(Color::Blue),
                    MessageRole::System => Style::default().fg(Color::Yellow),
                    MessageRole::Error => Style::default().fg(Color::Red),
                };

                let role_prefix = match msg.role {
                    MessageRole::User => "👤 You",
                    MessageRole::Assistant => "🤖 Arien",
                    MessageRole::System => "ℹ️  System",
                    MessageRole::Error => "❌ Error",
                };

                let timestamp = msg.timestamp.format("%H:%M:%S");
                
                ListItem::new(vec![
                    Line::from(vec![
                        Span::styled(format!("[{}] {}: ", timestamp, role_prefix), style),
                    ]),
                    Line::from(Span::styled(&msg.content, style)),
                    Line::from(""),
                ])
            })
            .collect();

        let messages_list = List::new(visible_messages)
            .block(messages_block)
            .style(Style::default().fg(Color::White));

        f.render_widget(messages_list, area);
    }

    fn draw_input<B: Backend>(&self, f: &mut Frame<B>, area: Rect) {
        let input_block = Block::default()
            .title("Message (Enter to send, Ctrl+U to clear)")
            .borders(Borders::ALL)
            .style(Style::default().fg(Color::Green));

        let input_text = Paragraph::new(self.input_buffer.as_str())
            .block(input_block)
            .wrap(Wrap { trim: true })
            .style(Style::default().fg(Color::White));

        f.render_widget(input_text, area);
    }

    fn add_message(&mut self, role: MessageRole, content: String) {
        self.messages.push(ChatMessage {
            role,
            content,
            timestamp: chrono::Utc::now(),
        });

        // Auto-scroll to bottom
        if self.messages.len() > 10 {
            self.scroll_offset = self.messages.len() - 10;
        }
    }
}

impl Default for ChatWidget {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_chat_widget_creation() {
        let widget = ChatWidget::new();
        assert_eq!(widget.messages.len(), 1);
        assert_eq!(widget.messages[0].role, MessageRole::System);
    }

    #[test]
    fn test_add_message() {
        let mut widget = ChatWidget::new();
        widget.add_message(MessageRole::User, "Hello".to_string());
        
        assert_eq!(widget.messages.len(), 2);
        assert_eq!(widget.messages[1].role, MessageRole::User);
        assert_eq!(widget.messages[1].content, "Hello");
    }
}
