use serde::{Deserialize, Serialize};

/// Application events for the TUI
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum AppEvent {
    /// Model response received
    ModelResponse {
        content: String,
        partial: bool,
    },
    
    /// Error occurred
    Error {
        message: String,
    },
    
    /// Informational message
    Info {
        message: String,
    },
    
    /// Tool call requested
    ToolCall {
        tool_name: String,
        arguments: std::collections::HashMap<String, serde_json::Value>,
    },
    
    /// Execution requested
    ExecutionRequest {
        command: Vec<String>,
        requires_approval: bool,
    },
    
    /// Patch requested
    PatchRequest {
        patch_id: String,
        description: String,
        requires_approval: bool,
    },
}

impl AppEvent {
    pub fn model_response(content: impl Into<String>, partial: bool) -> Self {
        Self::ModelResponse {
            content: content.into(),
            partial,
        }
    }

    pub fn error(message: impl Into<String>) -> Self {
        Self::Error {
            message: message.into(),
        }
    }

    pub fn info(message: impl Into<String>) -> Self {
        Self::Info {
            message: message.into(),
        }
    }

    pub fn tool_call(tool_name: impl Into<String>, arguments: std::collections::HashMap<String, serde_json::Value>) -> Self {
        Self::ToolCall {
            tool_name: tool_name.into(),
            arguments,
        }
    }

    pub fn execution_request(command: Vec<String>, requires_approval: bool) -> Self {
        Self::ExecutionRequest {
            command,
            requires_approval,
        }
    }

    pub fn patch_request(patch_id: impl Into<String>, description: impl Into<String>, requires_approval: bool) -> Self {
        Self::PatchRequest {
            patch_id: patch_id.into(),
            description: description.into(),
            requires_approval,
        }
    }
}
