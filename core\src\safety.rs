use arien_common::{Result, <PERSON>enError};

/// Safety assessment for commands and operations
pub struct SafetyAssessor;

impl SafetyAssessor {
    /// Assess if a command is safe to execute
    pub fn assess_command_safety(command: &[String]) -> Result<SafetyLevel> {
        if command.is_empty() {
            return Err(ArienError::invalid_input("Empty command"));
        }

        let command_name = &command[0];
        let args = &command[1..];

        // Check against known safe commands
        if Self::is_known_safe_command(command_name, args) {
            return Ok(SafetyLevel::Safe);
        }

        // Check against known dangerous commands
        if Self::is_known_dangerous_command(command_name, args) {
            return Ok(SafetyLevel::Dangerous);
        }

        // Default to unknown for commands we haven't categorized
        Ok(SafetyLevel::Unknown)
    }

    /// Check if a file path is safe to write to
    pub fn assess_file_write_safety(path: &std::path::Path, allowed_roots: &[std::path::PathBuf]) -> Result<SafetyLevel> {
        // Check if path is within allowed roots
        for root in allowed_roots {
            if path.starts_with(root) {
                return Ok(SafetyLevel::Safe);
            }
        }

        // Check for dangerous system paths
        let path_str = path.to_string_lossy().to_lowercase();
        if path_str.contains("/etc/") || 
           path_str.contains("/bin/") || 
           path_str.contains("/usr/bin/") ||
           path_str.contains("/system/") ||
           path_str.contains("c:\\windows\\") ||
           path_str.contains("c:\\program files\\") {
            return Ok(SafetyLevel::Dangerous);
        }

        Ok(SafetyLevel::Unknown)
    }

    fn is_known_safe_command(command: &str, args: &[String]) -> bool {
        match command {
            // File system read operations
            "ls" | "dir" | "cat" | "type" | "head" | "tail" | "less" | "more" => true,
            
            // Information commands
            "pwd" | "whoami" | "id" | "date" | "uptime" | "uname" => true,
            
            // Safe utilities
            "echo" | "printf" | "wc" | "grep" | "find" | "which" | "where" => true,
            
            // Version checks
            "node" | "npm" | "python" | "pip" | "cargo" | "rustc" | "git" => {
                args.iter().any(|arg| arg == "--version" || arg == "-V" || arg == "version")
            }
            
            _ => false,
        }
    }

    fn is_known_dangerous_command(command: &str, args: &[String]) -> bool {
        match command {
            // File deletion
            "rm" | "rmdir" | "del" | "rd" => true,
            
            // System modification
            "sudo" | "su" | "chmod" | "chown" | "chgrp" => true,
            
            // Network operations that could be dangerous
            "curl" | "wget" | "nc" | "netcat" => {
                // Allow safe read-only operations
                !args.iter().any(|arg| arg.starts_with("http://") || arg.starts_with("https://"))
            }
            
            // Process management
            "kill" | "killall" | "pkill" | "taskkill" => true,
            
            // System control
            "shutdown" | "reboot" | "halt" | "systemctl" | "service" => true,
            
            // Package managers (can modify system)
            "apt" | "yum" | "dnf" | "pacman" | "brew" => {
                // Allow info commands
                !args.iter().any(|arg| matches!(arg.as_str(), "search" | "info" | "show" | "list"))
            }
            
            _ => false,
        }
    }
}

/// Safety level for operations
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SafetyLevel {
    Safe,
    Unknown,
    Dangerous,
}

/// Safety policy for different types of operations
#[derive(Debug, Clone)]
pub struct SafetyPolicy {
    pub allow_unknown_commands: bool,
    pub allow_network_access: bool,
    pub allow_file_system_writes: bool,
    pub allowed_write_paths: Vec<std::path::PathBuf>,
    pub blocked_commands: Vec<String>,
    pub allowed_commands: Vec<String>,
}

impl Default for SafetyPolicy {
    fn default() -> Self {
        Self {
            allow_unknown_commands: false,
            allow_network_access: true,
            allow_file_system_writes: true,
            allowed_write_paths: vec![std::env::current_dir().unwrap_or_else(|_| std::path::PathBuf::from("."))],
            blocked_commands: vec![
                "rm".to_string(),
                "sudo".to_string(),
                "shutdown".to_string(),
            ],
            allowed_commands: vec![
                "ls".to_string(),
                "cat".to_string(),
                "echo".to_string(),
                "pwd".to_string(),
            ],
        }
    }
}

impl SafetyPolicy {
    /// Check if a command is allowed by this policy
    pub fn is_command_allowed(&self, command: &[String]) -> bool {
        if command.is_empty() {
            return false;
        }

        let command_name = &command[0];

        // Check blocked commands first
        if self.blocked_commands.contains(command_name) {
            return false;
        }

        // Check allowed commands
        if self.allowed_commands.contains(command_name) {
            return true;
        }

        // Check safety assessment
        match SafetyAssessor::assess_command_safety(command) {
            Ok(SafetyLevel::Safe) => true,
            Ok(SafetyLevel::Dangerous) => false,
            Ok(SafetyLevel::Unknown) => self.allow_unknown_commands,
            Err(_) => false,
        }
    }

    /// Check if a file write is allowed by this policy
    pub fn is_file_write_allowed(&self, path: &std::path::Path) -> bool {
        if !self.allow_file_system_writes {
            return false;
        }

        match SafetyAssessor::assess_file_write_safety(path, &self.allowed_write_paths) {
            Ok(SafetyLevel::Safe) => true,
            Ok(SafetyLevel::Dangerous) => false,
            Ok(SafetyLevel::Unknown) => false, // Conservative default
            Err(_) => false,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_safe_command_assessment() {
        let command = vec!["ls".to_string(), "-la".to_string()];
        let safety = SafetyAssessor::assess_command_safety(&command).unwrap();
        assert_eq!(safety, SafetyLevel::Safe);
    }

    #[test]
    fn test_dangerous_command_assessment() {
        let command = vec!["rm".to_string(), "-rf".to_string(), "/".to_string()];
        let safety = SafetyAssessor::assess_command_safety(&command).unwrap();
        assert_eq!(safety, SafetyLevel::Dangerous);
    }

    #[test]
    fn test_unknown_command_assessment() {
        let command = vec!["unknown_command".to_string()];
        let safety = SafetyAssessor::assess_command_safety(&command).unwrap();
        assert_eq!(safety, SafetyLevel::Unknown);
    }

    #[test]
    fn test_safety_policy() {
        let policy = SafetyPolicy::default();
        
        // Safe command should be allowed
        let safe_cmd = vec!["ls".to_string()];
        assert!(policy.is_command_allowed(&safe_cmd));
        
        // Blocked command should not be allowed
        let blocked_cmd = vec!["rm".to_string()];
        assert!(!policy.is_command_allowed(&blocked_cmd));
    }

    #[test]
    fn test_file_write_safety() {
        let allowed_roots = vec![std::path::PathBuf::from("/tmp")];
        
        // Safe path
        let safe_path = std::path::Path::new("/tmp/test.txt");
        let safety = SafetyAssessor::assess_file_write_safety(safe_path, &allowed_roots).unwrap();
        assert_eq!(safety, SafetyLevel::Safe);
        
        // Dangerous path
        let dangerous_path = std::path::Path::new("/etc/passwd");
        let safety = SafetyAssessor::assess_file_write_safety(dangerous_path, &allowed_roots).unwrap();
        assert_eq!(safety, SafetyLevel::Dangerous);
    }
}
