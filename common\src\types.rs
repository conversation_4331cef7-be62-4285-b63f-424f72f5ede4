use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// Unique identifier for submissions
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct SubmissionId(pub u64);

impl SubmissionId {
    pub fn new(id: u64) -> Self {
        Self(id)
    }
}

impl std::fmt::Display for SubmissionId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// Session identifier
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct SessionId(pub Uuid);

impl SessionId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
}

impl Default for SessionId {
    fn default() -> Self {
        Self::new()
    }
}

impl std::fmt::Display for SessionId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// Tool call identifier
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ToolCallId(pub String);

impl ToolCallId {
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }
}

impl std::fmt::Display for ToolCallId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// Timestamp wrapper
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct Timestamp(pub DateTime<Utc>);

impl Timestamp {
    pub fn now() -> Self {
        Self(Utc::now())
    }
}

impl Default for Timestamp {
    fn default() -> Self {
        Self::now()
    }
}

/// Generic key-value metadata
pub type Metadata = HashMap<String, serde_json::Value>;

/// File path wrapper with validation
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct SafePath(pub std::path::PathBuf);

impl SafePath {
    pub fn new(path: impl Into<std::path::PathBuf>) -> crate::Result<Self> {
        let path = path.into();
        
        // Basic validation - no parent directory traversal
        if path.components().any(|c| matches!(c, std::path::Component::ParentDir)) {
            return Err(crate::ArienError::invalid_input("Path contains parent directory traversal"));
        }
        
        Ok(Self(path))
    }
    
    pub fn as_path(&self) -> &std::path::Path {
        &self.0
    }
    
    pub fn into_path_buf(self) -> std::path::PathBuf {
        self.0
    }
}

impl std::fmt::Display for SafePath {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0.display())
    }
}

/// Content type for various inputs
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ContentType {
    Text,
    Image,
    Binary,
    Json,
    Yaml,
    Toml,
    Code(String), // Language identifier
}

impl ContentType {
    pub fn from_extension(ext: &str) -> Self {
        match ext.to_lowercase().as_str() {
            "txt" | "md" | "markdown" => Self::Text,
            "png" | "jpg" | "jpeg" | "gif" | "bmp" | "webp" => Self::Image,
            "json" => Self::Json,
            "yaml" | "yml" => Self::Yaml,
            "toml" => Self::Toml,
            "rs" => Self::Code("rust".to_string()),
            "py" => Self::Code("python".to_string()),
            "js" | "ts" => Self::Code("javascript".to_string()),
            "go" => Self::Code("go".to_string()),
            "c" | "h" => Self::Code("c".to_string()),
            "cpp" | "cc" | "cxx" | "hpp" => Self::Code("cpp".to_string()),
            "java" => Self::Code("java".to_string()),
            "sh" | "bash" => Self::Code("bash".to_string()),
            _ => Self::Binary,
        }
    }
}
