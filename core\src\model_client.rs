use arien_common::{Result, ArienError};
use crate::{Config, ModelProviderInfo, WireApi};
use reqwest::Client;
use serde_json::Value;

/// Model client for communicating with AI providers
pub struct ModelClient {
    config: Config,
    provider_info: ModelProviderInfo,
    model: String,
    client: Client,
    api_key: String,
}

impl ModelClient {
    /// Create a new model client
    pub async fn new(config: Config) -> Result<Self> {
        let provider_info = config.get_model_provider()?.clone();
        let api_key = std::env::var(&provider_info.api_key_env_var)
            .map_err(|_| ArienError::EnvVar(provider_info.api_key_env_var.clone()))?;

        let client = Client::new();
        let model = config.model.clone();

        Ok(Self {
            config,
            provider_info,
            model,
            client,
            api_key,
        })
    }

    /// Set the model to use
    pub async fn set_model(&mut self, model: String) -> Result<()> {
        self.model = model;
        Ok(())
    }

    /// Set the provider to use
    pub async fn set_provider(&mut self, provider_name: String) -> Result<()> {
        let provider_info = self.config.model_providers.get(&provider_name)
            .ok_or_else(|| ArienError::config(format!("Provider '{}' not found", provider_name)))?
            .clone();

        let api_key = std::env::var(&provider_info.api_key_env_var)
            .map_err(|_| ArienError::EnvVar(provider_info.api_key_env_var.clone()))?;

        self.provider_info = provider_info;
        self.api_key = api_key;
        
        Ok(())
    }

    /// Send a chat completion request
    pub async fn chat_completion(&self, messages: Vec<ChatMessage>) -> Result<String> {
        match self.provider_info.wire_api {
            WireApi::OpenAI => self.openai_chat_completion(messages).await,
            WireApi::Anthropic => self.anthropic_chat_completion(messages).await,
            WireApi::Custom => Err(ArienError::unknown("Custom API not implemented")),
        }
    }

    /// Stream a chat completion request
    pub async fn stream_chat_completion(&self, messages: Vec<ChatMessage>) -> Result<impl futures::Stream<Item = Result<String>>> {
        // Placeholder - would return a stream of partial responses
        Err(ArienError::unknown("Streaming not yet implemented"))
    }

    async fn openai_chat_completion(&self, messages: Vec<ChatMessage>) -> Result<String> {
        let request_body = serde_json::json!({
            "model": self.model,
            "messages": messages.into_iter().map(|m| serde_json::json!({
                "role": m.role,
                "content": m.content
            })).collect::<Vec<_>>(),
            "temperature": 0.7,
            "max_tokens": 4000
        });

        let response = self.client
            .post(&format!("{}/chat/completions", self.provider_info.base_url))
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await
            .map_err(ArienError::from)?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(ArienError::unknown(format!("API request failed: {}", error_text)));
        }

        let response_json: Value = response.json().await.map_err(ArienError::from)?;
        
        let content = response_json
            .get("choices")
            .and_then(|choices| choices.get(0))
            .and_then(|choice| choice.get("message"))
            .and_then(|message| message.get("content"))
            .and_then(|content| content.as_str())
            .ok_or_else(|| ArienError::unknown("Invalid response format"))?;

        Ok(content.to_string())
    }

    async fn anthropic_chat_completion(&self, messages: Vec<ChatMessage>) -> Result<String> {
        // Convert messages to Anthropic format
        let mut system_message = None;
        let mut conversation_messages = Vec::new();

        for message in messages {
            match message.role.as_str() {
                "system" => system_message = Some(message.content),
                "user" | "assistant" => conversation_messages.push(serde_json::json!({
                    "role": message.role,
                    "content": message.content
                })),
                _ => {} // Skip unknown roles
            }
        }

        let mut request_body = serde_json::json!({
            "model": self.model,
            "messages": conversation_messages,
            "max_tokens": 4000
        });

        if let Some(system) = system_message {
            request_body["system"] = serde_json::Value::String(system);
        }

        let response = self.client
            .post(&format!("{}/v1/messages", self.provider_info.base_url))
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .header("anthropic-version", "2023-06-01")
            .json(&request_body)
            .send()
            .await
            .map_err(ArienError::from)?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(ArienError::unknown(format!("API request failed: {}", error_text)));
        }

        let response_json: Value = response.json().await.map_err(ArienError::from)?;
        
        let content = response_json
            .get("content")
            .and_then(|content| content.get(0))
            .and_then(|block| block.get("text"))
            .and_then(|text| text.as_str())
            .ok_or_else(|| ArienError::unknown("Invalid response format"))?;

        Ok(content.to_string())
    }
}

/// Chat message for model requests
#[derive(Debug, Clone)]
pub struct ChatMessage {
    pub role: String,
    pub content: String,
}

impl ChatMessage {
    pub fn system(content: impl Into<String>) -> Self {
        Self {
            role: "system".to_string(),
            content: content.into(),
        }
    }

    pub fn user(content: impl Into<String>) -> Self {
        Self {
            role: "user".to_string(),
            content: content.into(),
        }
    }

    pub fn assistant(content: impl Into<String>) -> Self {
        Self {
            role: "assistant".to_string(),
            content: content.into(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_chat_message_creation() {
        let system_msg = ChatMessage::system("You are a helpful assistant");
        assert_eq!(system_msg.role, "system");
        assert_eq!(system_msg.content, "You are a helpful assistant");

        let user_msg = ChatMessage::user("Hello");
        assert_eq!(user_msg.role, "user");
        assert_eq!(user_msg.content, "Hello");

        let assistant_msg = ChatMessage::assistant("Hi there!");
        assert_eq!(assistant_msg.role, "assistant");
        assert_eq!(assistant_msg.content, "Hi there!");
    }
}
