use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use arien_common::{Result, ArienError, EnvUtils};
use crate::{AskForApproval, SandboxPolicy, ShellEnvironmentPolicy, McpServerConfig};

/// Main configuration structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub model: String,
    pub model_provider: String,
    pub instructions: Option<String>,
    pub approval_policy: AskForApproval,
    pub sandbox_policy: SandboxPolicy,
    pub shell_environment_policy: ShellEnvironmentPolicy,
    pub writable_roots: Vec<PathBuf>,
    pub mcp_servers: HashMap<String, McpServerConfig>,
    pub model_providers: HashMap<String, ModelProviderInfo>,
    pub profiles: HashMap<String, ConfigProfile>,
    pub disable_response_storage: bool,
    pub log_level: String,
}

/// Model provider information
#[derive(Debug, <PERSON>lone, PartialEq, Serialize, Deserialize)]
pub struct ModelProviderInfo {
    pub wire_api: WireApi,
    pub base_url: String,
    pub api_key_env_var: String,
    pub default_model: String,
}

/// Wire API type
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum WireApi {
    OpenAI,
    Anthropic,
    Custom,
}

/// Configuration profile
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigProfile {
    pub model: Option<String>,
    pub model_provider: Option<String>,
    pub approval_policy: Option<AskForApproval>,
    pub disable_response_storage: Option<bool>,
    pub instructions: Option<String>,
    pub sandbox_policy: Option<SandboxPolicy>,
    pub shell_environment_policy: Option<ShellEnvironmentPolicy>,
    pub writable_roots: Option<Vec<PathBuf>>,
    pub mcp_servers: Option<HashMap<String, McpServerConfig>>,
}

/// Configuration override for CLI
#[derive(Debug, Clone)]
pub struct ConfigOverride {
    pub key: String,
    pub value: String,
}

impl Config {
    /// Load configuration from default locations
    pub async fn load() -> Result<Self> {
        Self::load_with_overrides(Vec::new(), None).await
    }

    /// Load configuration with CLI overrides and optional profile
    pub async fn load_with_overrides(
        overrides: Vec<ConfigOverride>,
        profile: Option<String>,
    ) -> Result<Self> {
        // Start with default configuration
        let mut config = Self::default();

        // Load from config file if it exists
        if let Ok(config_dir) = EnvUtils::config_dir() {
            let config_file = config_dir.join("config.toml");
            if config_file.exists() {
                let content = tokio::fs::read_to_string(&config_file).await
                    .map_err(|e| ArienError::config(format!("Failed to read config file: {}", e)))?;
                
                let file_config: Config = toml::from_str(&content)
                    .map_err(|e| ArienError::config(format!("Failed to parse config file: {}", e)))?;
                
                config = config.merge(file_config);
            }
        }

        // Apply profile if specified
        if let Some(profile_name) = profile {
            if let Some(profile) = config.profiles.get(&profile_name).cloned() {
                config = config.apply_profile(profile);
            } else {
                return Err(ArienError::config(format!("Profile '{}' not found", profile_name)));
            }
        }

        // Apply CLI overrides
        for override_item in overrides {
            config = config.apply_override(override_item)?;
        }

        // Validate configuration
        config.validate()?;

        Ok(config)
    }

    /// Merge with another configuration (other takes precedence)
    pub fn merge(mut self, other: Config) -> Self {
        if other.model != Self::default().model {
            self.model = other.model;
        }
        if other.model_provider != Self::default().model_provider {
            self.model_provider = other.model_provider;
        }
        if other.instructions.is_some() {
            self.instructions = other.instructions;
        }
        if other.approval_policy != Self::default().approval_policy {
            self.approval_policy = other.approval_policy;
        }
        if other.sandbox_policy != Self::default().sandbox_policy {
            self.sandbox_policy = other.sandbox_policy;
        }
        if other.shell_environment_policy != Self::default().shell_environment_policy {
            self.shell_environment_policy = other.shell_environment_policy;
        }
        if !other.writable_roots.is_empty() {
            self.writable_roots = other.writable_roots;
        }
        if !other.mcp_servers.is_empty() {
            self.mcp_servers = other.mcp_servers;
        }
        if !other.model_providers.is_empty() {
            self.model_providers.extend(other.model_providers);
        }
        if !other.profiles.is_empty() {
            self.profiles.extend(other.profiles);
        }
        if other.disable_response_storage != Self::default().disable_response_storage {
            self.disable_response_storage = other.disable_response_storage;
        }
        if other.log_level != Self::default().log_level {
            self.log_level = other.log_level;
        }

        self
    }

    /// Apply a configuration profile
    pub fn apply_profile(mut self, profile: ConfigProfile) -> Self {
        if let Some(model) = profile.model {
            self.model = model;
        }
        if let Some(model_provider) = profile.model_provider {
            self.model_provider = model_provider;
        }
        if let Some(approval_policy) = profile.approval_policy {
            self.approval_policy = approval_policy;
        }
        if let Some(disable_response_storage) = profile.disable_response_storage {
            self.disable_response_storage = disable_response_storage;
        }
        if let Some(instructions) = profile.instructions {
            self.instructions = Some(instructions);
        }
        if let Some(sandbox_policy) = profile.sandbox_policy {
            self.sandbox_policy = sandbox_policy;
        }
        if let Some(shell_environment_policy) = profile.shell_environment_policy {
            self.shell_environment_policy = shell_environment_policy;
        }
        if let Some(writable_roots) = profile.writable_roots {
            self.writable_roots = writable_roots;
        }
        if let Some(mcp_servers) = profile.mcp_servers {
            self.mcp_servers = mcp_servers;
        }

        self
    }

    /// Apply a CLI override
    pub fn apply_override(mut self, override_item: ConfigOverride) -> Result<Self> {
        match override_item.key.as_str() {
            "model" => self.model = override_item.value,
            "model_provider" => self.model_provider = override_item.value,
            "instructions" => self.instructions = Some(override_item.value),
            "approval_policy" => {
                self.approval_policy = match override_item.value.as_str() {
                    "never" => AskForApproval::Never,
                    "on_failure" => AskForApproval::OnFailure,
                    "unless_allow_listed" => AskForApproval::UnlessAllowListed,
                    "auto_edit" => AskForApproval::AutoEdit,
                    _ => return Err(ArienError::config(format!("Invalid approval policy: {}", override_item.value))),
                };
            }
            "disable_response_storage" => {
                self.disable_response_storage = override_item.value.parse()
                    .map_err(|_| ArienError::config("disable_response_storage must be true or false"))?;
            }
            "log_level" => self.log_level = override_item.value,
            _ => return Err(ArienError::config(format!("Unknown config key: {}", override_item.key))),
        }

        Ok(self)
    }

    /// Validate the configuration
    pub fn validate(&self) -> Result<()> {
        // Check if model provider exists
        if !self.model_providers.contains_key(&self.model_provider) {
            return Err(ArienError::config(format!("Model provider '{}' not found", self.model_provider)));
        }

        // Validate writable roots exist
        for root in &self.writable_roots {
            if !root.exists() {
                return Err(ArienError::config(format!("Writable root does not exist: {}", root.display())));
            }
        }

        Ok(())
    }

    /// Get the current model provider info
    pub fn get_model_provider(&self) -> Result<&ModelProviderInfo> {
        self.model_providers.get(&self.model_provider)
            .ok_or_else(|| ArienError::config(format!("Model provider '{}' not found", self.model_provider)))
    }
}

impl Default for Config {
    fn default() -> Self {
        let mut model_providers = HashMap::new();
        
        // Add built-in providers
        model_providers.insert("openai".to_string(), ModelProviderInfo {
            wire_api: WireApi::OpenAI,
            base_url: "https://api.openai.com/v1".to_string(),
            api_key_env_var: "OPENAI_API_KEY".to_string(),
            default_model: "gpt-4".to_string(),
        });

        model_providers.insert("anthropic".to_string(), ModelProviderInfo {
            wire_api: WireApi::Anthropic,
            base_url: "https://api.anthropic.com".to_string(),
            api_key_env_var: "ANTHROPIC_API_KEY".to_string(),
            default_model: "claude-3-sonnet-20240229".to_string(),
        });

        model_providers.insert("openrouter".to_string(), ModelProviderInfo {
            wire_api: WireApi::OpenAI,
            base_url: "https://openrouter.ai/api/v1".to_string(),
            api_key_env_var: "OPENROUTER_API_KEY".to_string(),
            default_model: "anthropic/claude-3-sonnet".to_string(),
        });

        model_providers.insert("deepseek".to_string(), ModelProviderInfo {
            wire_api: WireApi::OpenAI,
            base_url: "https://api.deepseek.com/v1".to_string(),
            api_key_env_var: "DEEPSEEK_API_KEY".to_string(),
            default_model: "deepseek-chat".to_string(),
        });

        Self {
            model: "claude-3-sonnet-20240229".to_string(),
            model_provider: "anthropic".to_string(),
            instructions: None,
            approval_policy: AskForApproval::default(),
            sandbox_policy: SandboxPolicy::default(),
            shell_environment_policy: ShellEnvironmentPolicy::default(),
            writable_roots: vec![std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."))],
            mcp_servers: HashMap::new(),
            model_providers,
            profiles: HashMap::new(),
            disable_response_storage: false,
            log_level: "info".to_string(),
        }
    }
}

impl ConfigOverride {
    pub fn parse(s: &str) -> Result<Self> {
        let parts: Vec<&str> = s.splitn(2, '=').collect();
        if parts.len() != 2 {
            return Err(ArienError::config("Override must be in format key=value"));
        }

        Ok(Self {
            key: parts[0].to_string(),
            value: parts[1].to_string(),
        })
    }
}
