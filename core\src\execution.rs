use arien_common::{Result, ArienError};
use crate::{SandboxPolicy, SandboxType, AskForApproval};

/// Command execution with security checks
pub struct CommandExecutor {
    sandbox_policy: SandboxPolicy,
    approval_policy: AskForApproval,
}

impl CommandExecutor {
    pub fn new(sandbox_policy: SandboxPolicy, approval_policy: AskForApproval) -> Self {
        Self {
            sandbox_policy,
            approval_policy,
        }
    }

    /// Execute a command with security checks
    pub async fn execute(&self, command: Vec<String>, working_dir: Option<std::path::PathBuf>) -> Result<ExecutionResult> {
        // Safety assessment
        let safety = self.assess_command_safety(&command)?;
        
        // Check if approval is needed
        let needs_approval = self.needs_approval(&command, &safety);
        
        if needs_approval {
            return Ok(ExecutionResult::NeedsApproval { command, working_dir });
        }

        // Execute the command
        self.execute_command(command, working_dir).await
    }

    fn assess_command_safety(&self, command: &[String]) -> Result<CommandSafety> {
        // Placeholder safety assessment
        let command_name = command.first()
            .ok_or_else(|| ArienError::invalid_input("Empty command"))?;

        let safety = match command_name.as_str() {
            "ls" | "cat" | "echo" | "pwd" | "whoami" => CommandSafety::Safe,
            "rm" | "rmdir" | "del" => CommandSafety::Dangerous,
            _ => CommandSafety::Unknown,
        };

        Ok(safety)
    }

    fn needs_approval(&self, command: &[String], safety: &CommandSafety) -> bool {
        match self.approval_policy {
            AskForApproval::Never => false,
            AskForApproval::OnFailure => false, // Execute with sandbox, escalate on failure
            AskForApproval::UnlessAllowListed => !matches!(safety, CommandSafety::Safe),
            AskForApproval::AutoEdit => {
                // Auto-approve file edits, ask for everything else
                !self.is_file_edit_command(command)
            }
        }
    }

    fn is_file_edit_command(&self, command: &[String]) -> bool {
        if let Some(cmd) = command.first() {
            matches!(cmd.as_str(), "nano" | "vim" | "emacs" | "code" | "edit")
        } else {
            false
        }
    }

    async fn execute_command(&self, command: Vec<String>, working_dir: Option<std::path::PathBuf>) -> Result<ExecutionResult> {
        // Placeholder execution - in a real implementation this would:
        // 1. Set up the sandbox based on sandbox_policy
        // 2. Execute the command in the sandbox
        // 3. Stream output back
        // 4. Handle timeouts and interrupts

        let output = format!("Executed: {}", command.join(" "));
        
        Ok(ExecutionResult::Completed {
            exit_code: 0,
            stdout: output,
            stderr: String::new(),
            duration_ms: 100,
        })
    }
}

/// Command safety assessment
#[derive(Debug, Clone, PartialEq)]
pub enum CommandSafety {
    Safe,
    Unknown,
    Dangerous,
}

/// Execution result
#[derive(Debug, Clone)]
pub enum ExecutionResult {
    NeedsApproval {
        command: Vec<String>,
        working_dir: Option<std::path::PathBuf>,
    },
    Completed {
        exit_code: i32,
        stdout: String,
        stderr: String,
        duration_ms: u64,
    },
    Failed {
        error: String,
    },
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::SandboxPolicy;

    #[test]
    fn test_command_safety_assessment() {
        let executor = CommandExecutor::new(
            SandboxPolicy::default(),
            AskForApproval::UnlessAllowListed,
        );

        let safe_cmd = vec!["ls".to_string(), "-la".to_string()];
        let safety = executor.assess_command_safety(&safe_cmd).unwrap();
        assert_eq!(safety, CommandSafety::Safe);

        let dangerous_cmd = vec!["rm".to_string(), "-rf".to_string(), "/".to_string()];
        let safety = executor.assess_command_safety(&dangerous_cmd).unwrap();
        assert_eq!(safety, CommandSafety::Dangerous);
    }

    #[test]
    fn test_approval_needed() {
        let executor = CommandExecutor::new(
            SandboxPolicy::default(),
            AskForApproval::UnlessAllowListed,
        );

        let safe_cmd = vec!["ls".to_string()];
        assert!(!executor.needs_approval(&safe_cmd, &CommandSafety::Safe));

        let dangerous_cmd = vec!["rm".to_string()];
        assert!(executor.needs_approval(&dangerous_cmd, &CommandSafety::Dangerous));
    }
}
