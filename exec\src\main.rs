use arien_core::{Config, <PERSON><PERSON>, Op, SessionConfig, InputItem};
use arien_common::{Result, SessionId};
use clap::Parser;

#[derive(Parser)]
#[command(name = "arien-exec")]
#[command(about = "Arien AI - Headless execution engine")]
struct Args {
    /// Input prompt
    prompt: String,
    
    /// Output format (json, text)
    #[arg(long, default_value = "text")]
    format: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();
    
    // Load configuration
    let config = Config::load().await?;
    
    // Create arien instance
    let arien = Arien::new(config).await?;
    let session_id = SessionId::new();
    
    // Configure session for headless mode
    let session_config = SessionConfig {
        model: None,
        model_provider: None,
        instructions: Some("You are a helpful AI assistant. Be concise and direct.".to_string()),
        approval_policy: Some(arien_core::AskForApproval::Never),
        sandbox_policy: None,
        shell_environment_policy: None,
        writable_roots: None,
        mcp_servers: None,
    };
    
    arien.submit(session_id, Op::ConfigureSession { config: session_config }).await?;
    
    // Wait for session configured
    arien.recv_event().await?;
    
    // Send prompt
    let items = vec![InputItem::Text { content: args.prompt }];
    arien.submit(session_id, Op::UserInput { items }).await?;
    
    // Collect response
    let mut response = String::new();
    
    loop {
        let event = arien.recv_event().await?;
        match event.event_type {
            arien_core::EventType::ModelResponse { content, partial } => {
                if partial {
                    response.push_str(&content);
                } else {
                    response.push_str(&content);
                }
            }
            arien_core::EventType::ConversationEnded => {
                break;
            }
            arien_core::EventType::Error { error, .. } => {
                eprintln!("Error: {}", error);
                std::process::exit(1);
            }
            _ => {
                // Ignore other events in headless mode
            }
        }
    }
    
    // Output response
    match args.format.as_str() {
        "json" => {
            let output = serde_json::json!({
                "response": response,
                "status": "success"
            });
            println!("{}", serde_json::to_string_pretty(&output)?);
        }
        "text" => {
            println!("{}", response);
        }
        _ => {
            eprintln!("Unknown format: {}", args.format);
            std::process::exit(1);
        }
    }
    
    Ok(())
}
