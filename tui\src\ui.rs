use ratatui::{
    style::{Color, Style},
    text::{Line, Span},
    widgets::{Block, Borders},
};

/// UI styling and theming utilities
pub struct Theme {
    pub primary: Color,
    pub secondary: Color,
    pub accent: Color,
    pub success: Color,
    pub warning: Color,
    pub error: Color,
    pub text: Color,
    pub background: Color,
}

impl Default for Theme {
    fn default() -> Self {
        Self {
            primary: Color::Cyan,
            secondary: Color::Blue,
            accent: Color::Magenta,
            success: Color::Green,
            warning: Color::Yellow,
            error: Color::Red,
            text: Color::White,
            background: Color::Black,
        }
    }
}

impl Theme {
    pub fn dark() -> Self {
        Self::default()
    }

    pub fn light() -> Self {
        Self {
            primary: Color::Blue,
            secondary: Color::Cyan,
            accent: Color::Magenta,
            success: Color::Green,
            warning: Color::Yellow,
            error: Color::Red,
            text: Color::Black,
            background: Color::White,
        }
    }
}

/// UI component builders
pub struct UiBuilder;

impl UiBuilder {
    pub fn block_with_title(title: &str, theme: &Theme) -> Block {
        Block::default()
            .title(title)
            .borders(Borders::ALL)
            .style(Style::default().fg(theme.primary))
    }

    pub fn success_block(title: &str, theme: &Theme) -> Block {
        Block::default()
            .title(title)
            .borders(Borders::ALL)
            .style(Style::default().fg(theme.success))
    }

    pub fn error_block(title: &str, theme: &Theme) -> Block {
        Block::default()
            .title(title)
            .borders(Borders::ALL)
            .style(Style::default().fg(theme.error))
    }

    pub fn warning_block(title: &str, theme: &Theme) -> Block {
        Block::default()
            .title(title)
            .borders(Borders::ALL)
            .style(Style::default().fg(theme.warning))
    }

    pub fn styled_line(text: &str, style: Style) -> Line {
        Line::from(vec![Span::styled(text, style)])
    }

    pub fn success_line(text: &str, theme: &Theme) -> Line {
        Self::styled_line(text, Style::default().fg(theme.success))
    }

    pub fn error_line(text: &str, theme: &Theme) -> Line {
        Self::styled_line(text, Style::default().fg(theme.error))
    }

    pub fn warning_line(text: &str, theme: &Theme) -> Line {
        Self::styled_line(text, Style::default().fg(theme.warning))
    }

    pub fn info_line(text: &str, theme: &Theme) -> Line {
        Self::styled_line(text, Style::default().fg(theme.secondary))
    }
}

/// Layout utilities
pub struct LayoutHelper;

impl LayoutHelper {
    /// Create a centered rect with the given percentage of the area
    pub fn centered_rect(percent_x: u16, percent_y: u16, r: ratatui::layout::Rect) -> ratatui::layout::Rect {
        use ratatui::layout::{Constraint, Direction, Layout};
        
        let popup_layout = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Percentage((100 - percent_y) / 2),
                Constraint::Percentage(percent_y),
                Constraint::Percentage((100 - percent_y) / 2),
            ])
            .split(r);

        Layout::default()
            .direction(Direction::Horizontal)
            .constraints([
                Constraint::Percentage((100 - percent_x) / 2),
                Constraint::Percentage(percent_x),
                Constraint::Percentage((100 - percent_x) / 2),
            ])
            .split(popup_layout[1])[1]
    }

    /// Create a popup rect
    pub fn popup_rect(r: ratatui::layout::Rect) -> ratatui::layout::Rect {
        Self::centered_rect(60, 40, r)
    }

    /// Create a modal rect
    pub fn modal_rect(r: ratatui::layout::Rect) -> ratatui::layout::Rect {
        Self::centered_rect(80, 60, r)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_theme_creation() {
        let theme = Theme::default();
        assert_eq!(theme.primary, Color::Cyan);
        
        let dark_theme = Theme::dark();
        assert_eq!(dark_theme.primary, Color::Cyan);
        
        let light_theme = Theme::light();
        assert_eq!(light_theme.primary, Color::Blue);
    }

    #[test]
    fn test_ui_builder() {
        let theme = Theme::default();
        let block = UiBuilder::block_with_title("Test", &theme);
        // Can't easily test the actual styling, but we can test it doesn't panic
        assert!(block.title().is_some());
    }
}
